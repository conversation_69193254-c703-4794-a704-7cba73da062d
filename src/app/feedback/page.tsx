'use client';

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useAuth } from '@/contexts/AuthContext';
import { FeedbackList } from '@/components/feedback/FeedbackList';
import { FeedbackForm } from '@/components/feedback/FeedbackForm';
import { FeedbackFilters } from '@/components/feedback/FeedbackFilters';

const Container = styled.div`
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
`;

const Title = styled.h1`
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
`;

const CreateButton = styled.button`
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background: #0056b3;
  }

  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
`;

const Content = styled.div`
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const Sidebar = styled.div`
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  height: fit-content;
`;

const MainContent = styled.div`
  background: white;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  min-height: 600px;
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: #666;
`;

const ErrorMessage = styled.div`
  background: #f8d7da;
  color: #721c24;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #f5c6cb;
`;

interface FeedbackFiltersState {
  type: string;
  personal: boolean;
  createdBy: string;
  assignedTo: string;
  organizationId: string;
  departmentId: string;
}

export default function FeedbackPage() {
  const { user } = useAuth();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [feedbacks, setFeedbacks] = useState([]);
  const [feedbackTypes, setFeedbackTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filters, setFilters] = useState<FeedbackFiltersState>({
    type: '',
    personal: true, // Default to personal feedback
    createdBy: '',
    assignedTo: '',
    organizationId: '',
    departmentId: '',
  });

  // Load feedback types on component mount
  useEffect(() => {
    loadFeedbackTypes();
  }, []);

  // Load feedbacks when filters change
  useEffect(() => {
    loadFeedbacks();
  }, [filters]);

  const loadFeedbackTypes = async () => {
    try {
      const response = await fetch('/api/v1/feedback-types');
      if (response.ok) {
        const data = await response.json();
        setFeedbackTypes(data.feedbackTypes);
      } else {
        console.error('Failed to load feedback types');
      }
    } catch (error) {
      console.error('Error loading feedback types:', error);
    }
  };

  const loadFeedbacks = async () => {
    try {
      setLoading(true);
      setError('');

      const params = new URLSearchParams();
      if (filters.type) params.append('type', filters.type);
      if (filters.personal) params.append('personal', 'true');
      if (filters.createdBy) params.append('createdBy', filters.createdBy);
      if (filters.assignedTo) params.append('assignedTo', filters.assignedTo);
      if (filters.organizationId) params.append('organizationId', filters.organizationId);
      if (filters.departmentId) params.append('departmentId', filters.departmentId);

      const response = await fetch(`/api/v1/feedback?${params.toString()}`);
      
      if (response.ok) {
        const data = await response.json();
        setFeedbacks(data.feedbacks);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to load feedback');
      }
    } catch (error) {
      console.error('Error loading feedback:', error);
      setError('Failed to load feedback');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateFeedback = async (feedbackData: any) => {
    try {
      const response = await fetch('/api/v1/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(feedbackData),
      });

      if (response.ok) {
        setShowCreateForm(false);
        loadFeedbacks(); // Reload feedback list
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create feedback');
      }
    } catch (error) {
      console.error('Error creating feedback:', error);
      throw error; // Re-throw to let form handle the error
    }
  };

  const handleFiltersChange = (newFilters: Partial<FeedbackFiltersState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const canCreateFeedback = () => {
    if (!user) return false;
    // Owner, admin, and member leaders can create feedback
    return user.userRole.isOwner || user.userRole.isAdmin || user.userRole.isMember;
  };

  if (!user) {
    return (
      <Container>
        <LoadingSpinner>Loading...</LoadingSpinner>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <Title>Feedback</Title>
        {canCreateFeedback() && (
          <CreateButton
            onClick={() => setShowCreateForm(true)}
            disabled={showCreateForm}
          >
            Create Feedback
          </CreateButton>
        )}
      </Header>

      {error && <ErrorMessage>{error}</ErrorMessage>}

      <Content>
        <Sidebar>
          <FeedbackFilters
            filters={filters}
            feedbackTypes={feedbackTypes}
            onFiltersChange={handleFiltersChange}
            userRole={user.userRole}
          />
        </Sidebar>

        <MainContent>
          {showCreateForm ? (
            <FeedbackForm
              feedbackTypes={feedbackTypes}
              onSubmit={handleCreateFeedback}
              onCancel={() => setShowCreateForm(false)}
              userRole={user.userRole}
            />
          ) : loading ? (
            <LoadingSpinner>Loading feedback...</LoadingSpinner>
          ) : (
            <FeedbackList
              feedbacks={feedbacks}
              currentUser={user}
              onFeedbackUpdate={loadFeedbacks}
            />
          )}
        </MainContent>
      </Content>
    </Container>
  );
}
