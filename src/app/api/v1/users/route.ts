import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateUser } from '@/lib/auth';
import { hasOrganizationAdminPrivileges, getUserOrganizationAdminPrivileges } from '@/lib/permissions';

/**
 * GET API to retrieve users for feedback assignment
 * 
 * Query parameters:
 * - organizationId: Filter users by organization
 * - departmentId: Filter users by department
 * - taskId: Get users assigned to a specific task
 * 
 * Permission levels:
 * - Regular users: Only users in their departments
 * - Admins: Users in their organizations
 * - Owners: Users in their owned organizations
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organizationId');
    const departmentId = searchParams.get('departmentId');
    const taskId = searchParams.get('taskId');

    let whereClause: any = {};

    // Get user's organization privileges
    const userPrivileges = await getUserOrganizationAdminPrivileges(auth.userId);

    if (taskId) {
      // Get users assigned to a specific task
      const task = await prisma.task.findUnique({
        where: { id: parseInt(taskId) },
        include: {
          taskAssignments: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                  imageUrl: true,
                  userRole: {
                    select: {
                      id: true,
                      name: true,
                      isOwner: true,
                      isAdmin: true,
                      isMember: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      if (!task) {
        return NextResponse.json({ error: 'Task not found' }, { status: 404 });
      }

      // Check if user has access to this task
      const hasAccess = auth.isOwner || auth.isAdmin || 
        await hasOrganizationAdminPrivileges(auth.userId, task.organizationId);

      if (!hasAccess) {
        return NextResponse.json({ error: 'Access denied' }, { status: 403 });
      }

      const users = task.taskAssignments.map(assignment => assignment.user);
      return NextResponse.json({ users });
    }

    if (organizationId) {
      // Get users in a specific organization
      const orgId = parseInt(organizationId);
      
      // Check if user has access to this organization
      if (!userPrivileges.allAdminOrganizations.includes(orgId)) {
        return NextResponse.json({ error: 'Access denied' }, { status: 403 });
      }

      whereClause = {
        departmentMembers: {
          some: {
            department: {
              organizationId: orgId,
            },
          },
        },
      };
    } else if (departmentId) {
      // Get users in a specific department
      const department = await prisma.department.findUnique({
        where: { id: parseInt(departmentId) },
        select: { organizationId: true },
      });

      if (!department) {
        return NextResponse.json({ error: 'Department not found' }, { status: 404 });
      }

      // Check if user has access to this department's organization
      if (!userPrivileges.allAdminOrganizations.includes(department.organizationId)) {
        return NextResponse.json({ error: 'Access denied' }, { status: 403 });
      }

      whereClause = {
        departmentMembers: {
          some: {
            departmentId: parseInt(departmentId),
          },
        },
      };
    } else {
      // Get users based on user's role and permissions
      if (auth.isOwner) {
        // Owner can see users in their owned organizations
        whereClause = {
          departmentMembers: {
            some: {
              department: {
                organizationId: { in: userPrivileges.ownedOrganizations },
              },
            },
          },
        };
      } else if (auth.isAdmin || userPrivileges.adminOrganizations.length > 0) {
        // Admin can see users in their admin organizations
        whereClause = {
          departmentMembers: {
            some: {
              department: {
                organizationId: { in: userPrivileges.allAdminOrganizations },
              },
            },
          },
        };
      } else {
        // Regular members can see users in their departments
        const userDepartments = await prisma.departmentMember.findMany({
          where: { userId: auth.userId },
          select: { departmentId: true },
        });

        const departmentIds = userDepartments.map(dm => dm.departmentId);

        whereClause = {
          departmentMembers: {
            some: {
              departmentId: { in: departmentIds },
            },
          },
        };
      }
    }

    const users = await prisma.user.findMany({
      where: whereClause,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        imageUrl: true,
        userRole: {
          select: {
            id: true,
            name: true,
            isOwner: true,
            isAdmin: true,
            isMember: true,
          },
        },
      },
      orderBy: [
        { firstName: 'asc' },
        { lastName: 'asc' },
      ],
    });

    return NextResponse.json({ users });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
