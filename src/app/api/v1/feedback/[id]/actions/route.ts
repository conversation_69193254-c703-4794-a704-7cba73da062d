import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateUser } from '@/lib/auth';

/**
 * PATCH API for feedback user actions
 * 
 * This endpoint allows users to perform actions on feedback assigned to them:
 * - Accept feedback (isAccept: true)
 * - Discard feedback (isDiscard: true)
 * - Add reflection (reflection: "text")
 * - Share feedback (isShare: true)
 * 
 * URL: /api/v1/feedback/[id]/actions
 * 
 * Request body:
 * {
 *   "action": "accept" | "discard" | "reflect" | "share",
 *   "reflection": "Optional reflection text for reflect action",
 *   "value": true | false // For accept, discard, share actions
 * }
 * 
 * Only users who are assigned to the feedback can perform these actions.
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const feedbackId = parseInt(params.id);
    const body = await request.json();
    const { action, reflection, value } = body;

    if (!action) {
      return NextResponse.json({ error: 'Action is required' }, { status: 400 });
    }

    // Check if user is assigned to this feedback
    const feedbackUser = await prisma.feedbackUser.findFirst({
      where: {
        feedbackId,
        userId: auth.userId,
      },
    });

    if (!feedbackUser) {
      return NextResponse.json(
        { error: 'You are not assigned to this feedback' },
        { status: 403 }
      );
    }

    // Prepare update data based on action
    let updateData: any = {};

    switch (action) {
      case 'accept':
        if (typeof value !== 'boolean') {
          return NextResponse.json(
            { error: 'Value must be boolean for accept action' },
            { status: 400 }
          );
        }
        updateData.isAccept = value;
        // If accepting, clear discard
        if (value) {
          updateData.isDiscard = false;
        }
        break;

      case 'discard':
        if (typeof value !== 'boolean') {
          return NextResponse.json(
            { error: 'Value must be boolean for discard action' },
            { status: 400 }
          );
        }
        updateData.isDiscard = value;
        // If discarding, clear accept
        if (value) {
          updateData.isAccept = false;
        }
        break;

      case 'reflect':
        if (!reflection || typeof reflection !== 'string') {
          return NextResponse.json(
            { error: 'Reflection text is required for reflect action' },
            { status: 400 }
          );
        }
        updateData.reflection = reflection;
        break;

      case 'share':
        if (typeof value !== 'boolean') {
          return NextResponse.json(
            { error: 'Value must be boolean for share action' },
            { status: 400 }
          );
        }
        updateData.isShare = value;
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid action. Must be accept, discard, reflect, or share' },
          { status: 400 }
        );
    }

    // Update feedback user record
    const updatedFeedbackUser = await prisma.feedbackUser.update({
      where: { id: feedbackUser.id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
          },
        },
        feedback: {
          include: {
            createFrom: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                imageUrl: true,
              },
            },
            feedback_types: true,
          },
        },
      },
    });

    return NextResponse.json({
      feedbackUser: updatedFeedbackUser,
      message: `Feedback ${action} action completed successfully`,
    });
  } catch (error) {
    console.error('Error updating feedback user action:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET API to get feedback user status
 * 
 * Returns the current status of the authenticated user for the specified feedback
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const feedbackId = parseInt(params.id);

    // Get feedback user record
    const feedbackUser = await prisma.feedbackUser.findFirst({
      where: {
        feedbackId,
        userId: auth.userId,
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
          },
        },
      },
    });

    if (!feedbackUser) {
      return NextResponse.json(
        { error: 'You are not assigned to this feedback' },
        { status: 403 }
      );
    }

    return NextResponse.json({ feedbackUser });
  } catch (error) {
    console.error('Error fetching feedback user status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
