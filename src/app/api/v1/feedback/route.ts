import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateUser } from '@/lib/auth';
import { hasOrganizationAdminPrivileges, getUserOrganizationAdminPrivileges } from '@/lib/permissions';
import {
  canAssignToUsers,
  isUserDepartmentLeader,
  canModifyFeedback,
  getFeedbackUserOrganizationAdminPrivileges
} from '@/lib/feedback-permissions';

/**
 * GET API for feedback
 * 
 * Query parameters:
 * - id: Get specific feedback by ID
 * - type: Filter by feedback type (private, task, department, organization)
 * - createdBy: Filter by creator (for admin/owner access)
 * - assignedTo: Filter by assigned user (for admin/owner access)
 * - organizationId: Filter by organization (for admin/owner access)
 * - departmentId: Filter by department (for admin/owner access)
 * - personal: Get personal feedback (created by me, assigned to me, shared with me)
 * 
 * Permission levels:
 * - Regular users: Only personal feedback
 * - Member leaders: Department feedback + personal
 * - Admins: Organization feedback + department feedback + personal
 * - Owners: All feedback in organization
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    const type = searchParams.get('type');
    const createdBy = searchParams.get('createdBy');
    const assignedTo = searchParams.get('assignedTo');
    const organizationId = searchParams.get('organizationId');
    const departmentId = searchParams.get('departmentId');
    const personal = searchParams.get('personal');

    // Get single feedback by ID
    if (id) {
      const feedback = await prisma.feedback.findUnique({
        where: { id: parseInt(id) },
        include: {
          createFrom: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              imageUrl: true,
              userRole: {
                select: {
                  id: true,
                  name: true,
                  isOwner: true,
                  isAdmin: true,
                  isMember: true,
                },
              },
            },
          },
          feedbackUsers: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                  imageUrl: true,
                  userRole: {
                    select: {
                      id: true,
                      name: true,
                      isOwner: true,
                      isAdmin: true,
                      isMember: true,
                    },
                  },
                },
              },
            },
          },
          feedback_types: true,
          tasks: {
            select: {
              id: true,
              taskTitle: true,
            },
          },
          departments: {
            select: {
              id: true,
              name: true,
            },
          },
          organizations: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (!feedback) {
        return NextResponse.json({ error: 'Feedback not found' }, { status: 404 });
      }

      // Check access permissions
      const canAccess = await canAccessFeedback(auth.userId, feedback, auth);
      if (!canAccess) {
        return NextResponse.json({ error: 'Access denied' }, { status: 403 });
      }

      return NextResponse.json({ feedback });
    }

    // Get list of feedback with filters
    let whereClause: any = {};

    // Personal feedback filter
    if (personal === 'true') {
      whereClause = {
        OR: [
          { createFromId: auth.userId }, // Created by me
          { feedbackUsers: { some: { userId: auth.userId } } }, // Assigned to me
          { feedbackUsers: { some: { userId: auth.userId, isShare: true } } }, // Shared with me
        ],
      };
    } else {
      // Apply role-based filtering for non-personal queries
      const userPrivileges = await getUserOrganizationAdminPrivileges(auth.userId);
      
      if (auth.isOwner) {
        // Owner can see all feedback in their organizations
        if (organizationId) {
          whereClause.organization_id = parseInt(organizationId);
        } else {
          whereClause.organization_id = { in: userPrivileges.ownedOrganizations };
        }
      } else if (auth.isAdmin || userPrivileges.adminOrganizations.length > 0) {
        // Admin can see organization and department feedback
        if (organizationId) {
          const hasAccess = await hasOrganizationAdminPrivileges(auth.userId, parseInt(organizationId));
          if (!hasAccess) {
            return NextResponse.json({ error: 'Access denied' }, { status: 403 });
          }
          whereClause.organization_id = parseInt(organizationId);
        } else if (departmentId) {
          whereClause.department_id = parseInt(departmentId);
        } else {
          whereClause.organization_id = { in: userPrivileges.allAdminOrganizations };
        }
      } else {
        // Regular members can only see personal feedback
        whereClause = {
          OR: [
            { createFromId: auth.userId },
            { feedbackUsers: { some: { userId: auth.userId } } },
          ],
        };
      }
    }

    // Apply additional filters
    if (type) {
      const feedbackType = await prisma.feedback_types.findUnique({
        where: { name: type },
      });
      if (feedbackType) {
        whereClause.type_id = feedbackType.id;
      }
    }

    if (createdBy) {
      whereClause.createFromId = parseInt(createdBy);
    }

    if (assignedTo) {
      whereClause.feedbackUsers = {
        some: { userId: parseInt(assignedTo) },
      };
    }

    const feedbacks = await prisma.feedback.findMany({
      where: whereClause,
      include: {
        createFrom: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
            userRole: {
              select: {
                id: true,
                name: true,
                isOwner: true,
                isAdmin: true,
                isMember: true,
              },
            },
          },
        },
        feedbackUsers: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                imageUrl: true,
              },
            },
          },
        },
        feedback_types: true,
        tasks: {
          select: {
            id: true,
            taskTitle: true,
          },
        },
        departments: {
          select: {
            id: true,
            name: true,
          },
        },
        organizations: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    return NextResponse.json({ feedbacks });
  } catch (error) {
    console.error('Error fetching feedback:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Helper function to check if user can access specific feedback
 */
async function canAccessFeedback(userId: number, feedback: any, auth: any): Promise<boolean> {
  // Creator can always access
  if (feedback.createFromId === userId) {
    return true;
  }

  // Assigned users can access
  const isAssigned = feedback.feedbackUsers.some((fu: any) => fu.userId === userId);
  if (isAssigned) {
    return true;
  }

  // Shared feedback access
  const isShared = feedback.feedbackUsers.some((fu: any) => fu.userId === userId && fu.isShare);
  if (isShared) {
    return true;
  }

  // Admin/Owner access based on organization
  if (auth.isOwner || auth.isAdmin) {
    if (feedback.organization_id) {
      return await hasOrganizationAdminPrivileges(userId, feedback.organization_id);
    }
  }

  return false;
}

/**
 * POST API to create new feedback
 *
 * Request body:
 * {
 *   "type": "private" | "task" | "department" | "organization",
 *   "situation": "Description of the situation",
 *   "behavior": "Observed behavior",
 *   "impact": "Impact description",
 *   "actionable": "Actionable items/suggestions",
 *   "appreciation": "Appreciation notes",
 *   "growthToken": "Growth token value",
 *   "assignedUserIds": [1, 2, 3], // Array of user IDs to assign feedback to
 *   "taskId": 123, // Optional: for task-type feedback
 *   "departmentId": 123, // Optional: for department-type feedback
 *   "organizationId": 123 // Optional: for organization-type feedback
 * }
 *
 * Permission requirements:
 * - private: Only owner, admin, and member leaders (within their scope)
 * - task: Only owner, admin, and member leaders (can assign to task members)
 * - department: Only owner and admin (can assign to department members)
 * - organization: Only owner (can assign to organization members)
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const {
      type,
      situation,
      behavior,
      impact,
      actionable,
      appreciation,
      growthToken,
      assignedUserIds = [],
      taskId,
      departmentId,
      organizationId,
    } = body;

    // Validate required fields
    if (!type || !assignedUserIds.length) {
      return NextResponse.json(
        { error: 'Type and assignedUserIds are required' },
        { status: 400 }
      );
    }

    // Get feedback type
    const feedbackType = await prisma.feedback_types.findUnique({
      where: { name: type },
    });

    if (!feedbackType) {
      return NextResponse.json(
        { error: 'Invalid feedback type' },
        { status: 400 }
      );
    }

    // Check permissions based on feedback type
    const canCreate = await canCreateFeedback(auth, type, {
      taskId,
      departmentId,
      organizationId,
      assignedUserIds,
    });

    if (!canCreate.allowed) {
      return NextResponse.json({ error: canCreate.error }, { status: 403 });
    }

    // Create feedback
    const feedback = await prisma.feedback.create({
      data: {
        type_id: feedbackType.id,
        createFromId: auth.userId,
        situation: situation || null,
        behavior: behavior || null,
        impact: impact || null,
        actionable: actionable || null,
        appreciation: appreciation || null,
        growthToken: growthToken || null,
        task_id: taskId ? parseInt(taskId) : null,
        department_id: departmentId ? parseInt(departmentId) : null,
        organization_id: organizationId ? parseInt(organizationId) : null,
        feedbackUsers: {
          create: assignedUserIds.map((userId: number) => ({
            userId: parseInt(userId.toString()),
          })),
        },
      },
      include: {
        createFrom: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
            userRole: {
              select: {
                id: true,
                name: true,
                isOwner: true,
                isAdmin: true,
                isMember: true,
              },
            },
          },
        },
        feedbackUsers: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                imageUrl: true,
              },
            },
          },
        },
        feedback_types: true,
        tasks: {
          select: {
            id: true,
            taskTitle: true,
          },
        },
        departments: {
          select: {
            id: true,
            name: true,
          },
        },
        organizations: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return NextResponse.json({ feedback }, { status: 201 });
  } catch (error) {
    console.error('Error creating feedback:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Helper function to check if user can create feedback of specific type
 */
async function canCreateFeedback(
  auth: any,
  type: string,
  context: {
    taskId?: number;
    departmentId?: number;
    organizationId?: number;
    assignedUserIds: number[];
  }
): Promise<{ allowed: boolean; error?: string }> {
  const { taskId, departmentId, organizationId, assignedUserIds } = context;

  switch (type) {
    case 'private':
      // Owner, admin, and member leaders can create private feedback
      if (auth.isOwner || auth.isAdmin) {
        // Check if they can assign to the selected users (within their organization)
        const userPrivileges = await getFeedbackUserOrganizationAdminPrivileges(auth.userId);
        const canAssignToUsers = await canAssignToUsers(
          assignedUserIds,
          userPrivileges.allAdminOrganizations
        );
        if (!canAssignToUsers) {
          return { allowed: false, error: 'Cannot assign feedback to users outside your organization' };
        }
        return { allowed: true };
      }

      // Member leaders can create private feedback within their department
      const isLeader = await isUserDepartmentLeader(auth.userId);
      if (isLeader.isLeader) {
        const canAssignToUsers = await canAssignToUsers(assignedUserIds, [], isLeader.departmentIds);
        if (!canAssignToUsers) {
          return { allowed: false, error: 'Cannot assign feedback to users outside your department' };
        }
        return { allowed: true };
      }

      return { allowed: false, error: 'Insufficient permissions to create private feedback' };

    case 'task':
      // Owner, admin, and member leaders can create task feedback
      if (!taskId) {
        return { allowed: false, error: 'Task ID is required for task feedback' };
      }

      if (auth.isOwner || auth.isAdmin) {
        // Check if they have access to the task
        const task = await prisma.task.findUnique({
          where: { id: taskId },
          select: { organizationId: true },
        });

        if (!task) {
          return { allowed: false, error: 'Task not found' };
        }

        const hasAccess = await hasOrganizationAdminPrivileges(auth.userId, task.organizationId);
        if (!hasAccess) {
          return { allowed: false, error: 'No access to this task' };
        }

        return { allowed: true };
      }

      // Member leaders can create task feedback for tasks in their department
      const leaderInfo = await isUserDepartmentLeader(auth.userId);
      if (leaderInfo.isLeader) {
        const task = await prisma.task.findUnique({
          where: { id: taskId },
          select: { departmentId: true },
        });

        if (!task || !leaderInfo.departmentIds.includes(task.departmentId)) {
          return { allowed: false, error: 'No access to this task' };
        }

        return { allowed: true };
      }

      return { allowed: false, error: 'Insufficient permissions to create task feedback' };

    case 'department':
      // Only owner and admin can create department feedback
      if (!auth.isOwner && !auth.isAdmin) {
        return { allowed: false, error: 'Only owners and admins can create department feedback' };
      }

      if (departmentId) {
        const department = await prisma.department.findUnique({
          where: { id: departmentId },
          select: { organizationId: true },
        });

        if (!department) {
          return { allowed: false, error: 'Department not found' };
        }

        const hasAccess = await hasOrganizationAdminPrivileges(auth.userId, department.organizationId);
        if (!hasAccess) {
          return { allowed: false, error: 'No access to this department' };
        }
      }

      return { allowed: true };

    case 'organization':
      // Only owner can create organization feedback
      if (!auth.isOwner) {
        return { allowed: false, error: 'Only owners can create organization feedback' };
      }

      if (organizationId) {
        const userPrivileges = await getFeedbackUserOrganizationAdminPrivileges(auth.userId);
        if (!userPrivileges.ownedOrganizations.includes(organizationId)) {
          return { allowed: false, error: 'You do not own this organization' };
        }
      }

      return { allowed: true };

    default:
      return { allowed: false, error: 'Invalid feedback type' };
  }
}

/**
 * PATCH API to update feedback
 *
 * Only feedback creator and organization owner can update feedback
 */
export async function PATCH(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const {
      id,
      situation,
      behavior,
      impact,
      actionable,
      appreciation,
      growthToken,
      assignedUserIds,
    } = body;

    if (!id) {
      return NextResponse.json({ error: 'Feedback ID is required' }, { status: 400 });
    }

    // Get existing feedback
    const existingFeedback = await prisma.feedback.findUnique({
      where: { id: parseInt(id) },
      include: {
        feedbackUsers: true,
      },
    });

    if (!existingFeedback) {
      return NextResponse.json({ error: 'Feedback not found' }, { status: 404 });
    }

    // Check permissions
    const canModify = await canModifyFeedback(auth.userId, existingFeedback, auth);
    if (!canModify) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Update feedback
    const updateData: any = {};
    if (situation !== undefined) updateData.situation = situation;
    if (behavior !== undefined) updateData.behavior = behavior;
    if (impact !== undefined) updateData.impact = impact;
    if (actionable !== undefined) updateData.actionable = actionable;
    if (appreciation !== undefined) updateData.appreciation = appreciation;
    if (growthToken !== undefined) updateData.growthToken = growthToken;

    // Handle assigned users update
    if (assignedUserIds && Array.isArray(assignedUserIds)) {
      // Delete existing assignments
      await prisma.feedbackUser.deleteMany({
        where: { feedbackId: parseInt(id) },
      });

      // Create new assignments
      updateData.feedbackUsers = {
        create: assignedUserIds.map((userId: number) => ({
          userId: parseInt(userId.toString()),
        })),
      };
    }

    const updatedFeedback = await prisma.feedback.update({
      where: { id: parseInt(id) },
      data: updateData,
      include: {
        createFrom: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
            userRole: {
              select: {
                id: true,
                name: true,
                isOwner: true,
                isAdmin: true,
                isMember: true,
              },
            },
          },
        },
        feedbackUsers: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                imageUrl: true,
              },
            },
          },
        },
        feedback_types: true,
        tasks: {
          select: {
            id: true,
            taskTitle: true,
          },
        },
        departments: {
          select: {
            id: true,
            name: true,
          },
        },
        organizations: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return NextResponse.json({ feedback: updatedFeedback });
  } catch (error) {
    console.error('Error updating feedback:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE API to delete feedback
 *
 * Only feedback creator and organization owner can delete feedback
 */
export async function DELETE(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Feedback ID is required' }, { status: 400 });
    }

    // Get existing feedback
    const existingFeedback = await prisma.feedback.findUnique({
      where: { id: parseInt(id) },
    });

    if (!existingFeedback) {
      return NextResponse.json({ error: 'Feedback not found' }, { status: 404 });
    }

    // Check permissions
    const canModify = await canModifyFeedback(auth.userId, existingFeedback, auth);
    if (!canModify) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Delete feedback (cascade will handle feedbackUsers)
    await prisma.feedback.delete({
      where: { id: parseInt(id) },
    });

    return NextResponse.json({ message: 'Feedback deleted successfully' });
  } catch (error) {
    console.error('Error deleting feedback:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
