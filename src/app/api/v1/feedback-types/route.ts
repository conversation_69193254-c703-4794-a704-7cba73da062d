import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateUser } from '@/lib/auth';

/**
 * GET API to retrieve all feedback types (master data)
 * 
 * This endpoint returns all available feedback types that can be used
 * when creating feedback. The types are:
 * - private: Private feedback between individuals
 * - task: Feedback related to specific tasks
 * - department: Feedback within department context
 * - organization: Organization-wide feedback
 * 
 * Authentication required.
 * 
 * Response format:
 * {
 *   "feedbackTypes": [
 *     {
 *       "id": 1,
 *       "name": "private",
 *       "description": "Private feedback between individuals",
 *       "createdAt": "2025-07-31T07:00:00.000Z",
 *       "updatedAt": "2025-07-31T07:00:00.000Z"
 *     },
 *     ...
 *   ]
 * }
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Get all feedback types
    const feedbackTypes = await prisma.feedback_types.findMany({
      orderBy: { id: 'asc' },
    });

    return NextResponse.json({
      feedbackTypes: feedbackTypes.map(type => ({
        id: type.id,
        name: type.name,
        description: type.description,
        createdAt: type.created_at,
        updatedAt: type.updated_at,
      })),
    });
  } catch (error) {
    console.error('Error fetching feedback types:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
