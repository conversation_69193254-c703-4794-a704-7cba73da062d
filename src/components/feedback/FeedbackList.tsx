'use client';

import React, { useState } from 'react';
import styled from 'styled-components';
import { FeedbackCard } from './FeedbackCard';
import { FeedbackDetail } from './FeedbackDetail';

const Container = styled.div`
  padding: 20px;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: #666;
`;

const EmptyIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
`;

const EmptyTitle = styled.h3`
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 8px 0;
  color: #333;
`;

const EmptyDescription = styled.p`
  font-size: 14px;
  margin: 0;
  color: #666;
`;

const FeedbackGrid = styled.div`
  display: grid;
  gap: 16px;
`;

const Modal = styled.div<{ isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: ${props => props.isOpen ? 'flex' : 'none'};
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
`;

const ModalContent = styled.div`
  background: white;
  border-radius: 12px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
`;

const CloseButton = styled.button`
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  z-index: 1;
  
  &:hover {
    color: #333;
  }
`;

interface FeedbackListProps {
  feedbacks: any[];
  currentUser: any;
  onFeedbackUpdate: () => void;
}

export const FeedbackList: React.FC<FeedbackListProps> = ({
  feedbacks,
  currentUser,
  onFeedbackUpdate,
}) => {
  const [selectedFeedback, setSelectedFeedback] = useState<any>(null);

  const handleFeedbackClick = (feedback: any) => {
    setSelectedFeedback(feedback);
  };

  const handleCloseDetail = () => {
    setSelectedFeedback(null);
  };

  const handleFeedbackAction = async (feedbackId: number, action: string, data: any) => {
    try {
      const response = await fetch(`/api/v1/feedback/${feedbackId}/actions`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          ...data,
        }),
      });

      if (response.ok) {
        onFeedbackUpdate(); // Refresh the feedback list
        
        // Update the selected feedback if it's currently open
        if (selectedFeedback && selectedFeedback.id === feedbackId) {
          const updatedResponse = await fetch(`/api/v1/feedback?id=${feedbackId}`);
          if (updatedResponse.ok) {
            const updatedData = await updatedResponse.json();
            setSelectedFeedback(updatedData.feedback);
          }
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to perform action');
      }
    } catch (error) {
      console.error('Error performing feedback action:', error);
      throw error;
    }
  };

  const handleFeedbackUpdate = async (feedbackId: number, updateData: any) => {
    try {
      const response = await fetch('/api/v1/feedback', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: feedbackId,
          ...updateData,
        }),
      });

      if (response.ok) {
        onFeedbackUpdate(); // Refresh the feedback list
        
        // Update the selected feedback if it's currently open
        if (selectedFeedback && selectedFeedback.id === feedbackId) {
          const updatedResponse = await fetch(`/api/v1/feedback?id=${feedbackId}`);
          if (updatedResponse.ok) {
            const updatedData = await updatedResponse.json();
            setSelectedFeedback(updatedData.feedback);
          }
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update feedback');
      }
    } catch (error) {
      console.error('Error updating feedback:', error);
      throw error;
    }
  };

  const handleFeedbackDelete = async (feedbackId: number) => {
    try {
      const response = await fetch(`/api/v1/feedback?id=${feedbackId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        onFeedbackUpdate(); // Refresh the feedback list
        
        // Close detail modal if the deleted feedback was selected
        if (selectedFeedback && selectedFeedback.id === feedbackId) {
          setSelectedFeedback(null);
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete feedback');
      }
    } catch (error) {
      console.error('Error deleting feedback:', error);
      throw error;
    }
  };

  if (feedbacks.length === 0) {
    return (
      <Container>
        <EmptyState>
          <EmptyIcon>💬</EmptyIcon>
          <EmptyTitle>No feedback found</EmptyTitle>
          <EmptyDescription>
            There are no feedback items matching your current filters.
            Try adjusting your filters or create new feedback.
          </EmptyDescription>
        </EmptyState>
      </Container>
    );
  }

  return (
    <Container>
      <FeedbackGrid>
        {feedbacks.map((feedback) => (
          <FeedbackCard
            key={feedback.id}
            feedback={feedback}
            currentUser={currentUser}
            onClick={() => handleFeedbackClick(feedback)}
            onAction={handleFeedbackAction}
          />
        ))}
      </FeedbackGrid>

      <Modal isOpen={!!selectedFeedback}>
        <ModalContent>
          <CloseButton onClick={handleCloseDetail}>×</CloseButton>
          {selectedFeedback && (
            <FeedbackDetail
              feedback={selectedFeedback}
              currentUser={currentUser}
              onAction={handleFeedbackAction}
              onUpdate={handleFeedbackUpdate}
              onDelete={handleFeedbackDelete}
              onClose={handleCloseDetail}
            />
          )}
        </ModalContent>
      </Modal>
    </Container>
  );
};
