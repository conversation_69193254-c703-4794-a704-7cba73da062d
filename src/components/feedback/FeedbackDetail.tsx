'use client';

import React, { useState } from 'react';
import styled from 'styled-components';

const Container = styled.div`
  padding: 24px;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e9ecef;
`;

const HeaderLeft = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const TypeBadge = styled.span<{ type: string }>`
  background: ${props => {
    switch (props.type) {
      case 'private': return '#e3f2fd';
      case 'task': return '#f3e5f5';
      case 'department': return '#e8f5e8';
      case 'organization': return '#fff3e0';
      default: return '#f5f5f5';
    }
  }};
  color: ${props => {
    switch (props.type) {
      case 'private': return '#1976d2';
      case 'task': return '#7b1fa2';
      case 'department': return '#388e3c';
      case 'organization': return '#f57c00';
      default: return '#666';
    }
  }};
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  text-transform: capitalize;
  width: fit-content;
`;

const CreatedBy = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  color: #333;
`;

const Avatar = styled.img`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
`;

const AvatarPlaceholder = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
`;

const CreatedAt = styled.div`
  font-size: 14px;
  color: #666;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 8px;
`;

const ActionButton = styled.button<{ variant?: 'primary' | 'danger' | 'secondary' }>`
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;

  ${props => {
    switch (props.variant) {
      case 'primary':
        return `
          background: #007bff;
          color: white;
          &:hover:not(:disabled) { background: #0056b3; }
        `;
      case 'danger':
        return `
          background: #dc3545;
          color: white;
          &:hover:not(:disabled) { background: #c82333; }
        `;
      default:
        return `
          background: #f8f9fa;
          color: #666;
          border: 1px solid #ddd;
          &:hover { border-color: #007bff; color: #007bff; }
        `;
    }
  }}

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const Content = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const ContentSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const SectionTitle = styled.h3`
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
`;

const ContentField = styled.div`
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
`;

const FieldLabel = styled.div`
  font-size: 12px;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
`;

const FieldValue = styled.div`
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  white-space: pre-wrap;
`;

const AssignedUsers = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const UserCard = styled.div`
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const UserDetails = styled.div`
  display: flex;
  flex-direction: column;
`;

const UserName = styled.span`
  font-size: 14px;
  font-weight: 500;
  color: #333;
`;

const UserEmail = styled.span`
  font-size: 12px;
  color: #666;
`;

const UserActions = styled.div`
  display: flex;
  gap: 8px;
  align-items: center;
`;

const StatusBadge = styled.span<{ status: 'accepted' | 'discarded' | 'reflected' | 'shared' }>`
  background: ${props => {
    switch (props.status) {
      case 'accepted': return '#d4edda';
      case 'discarded': return '#f8d7da';
      case 'reflected': return '#d1ecf1';
      case 'shared': return '#fff3cd';
      default: return '#f5f5f5';
    }
  }};
  color: ${props => {
    switch (props.status) {
      case 'accepted': return '#155724';
      case 'discarded': return '#721c24';
      case 'reflected': return '#0c5460';
      case 'shared': return '#856404';
      default: return '#666';
    }
  }};
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
`;

const ReflectionText = styled.div`
  font-size: 12px;
  color: #666;
  font-style: italic;
  margin-top: 4px;
`;

const UserActionButtons = styled.div`
  display: flex;
  gap: 4px;
`;

const SmallButton = styled.button<{ variant?: 'accept' | 'discard' | 'reflect' | 'share' }>`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;

  ${props => {
    switch (props.variant) {
      case 'accept':
        return `background: #28a745; color: white; &:hover { background: #218838; }`;
      case 'discard':
        return `background: #dc3545; color: white; &:hover { background: #c82333; }`;
      case 'reflect':
        return `background: #17a2b8; color: white; &:hover { background: #138496; }`;
      case 'share':
        return `background: #ffc107; color: #212529; &:hover { background: #e0a800; }`;
      default:
        return `background: #f8f9fa; color: #666; border: 1px solid #ddd;`;
    }
  }}
`;

interface FeedbackDetailProps {
  feedback: any;
  currentUser: any;
  onAction: (feedbackId: number, action: string, data: any) => Promise<void>;
  onUpdate: (feedbackId: number, data: any) => Promise<void>;
  onDelete: (feedbackId: number) => Promise<void>;
  onClose: () => void;
}

export const FeedbackDetail: React.FC<FeedbackDetailProps> = ({
  feedback,
  currentUser,
  onAction,
  onUpdate,
  onDelete,
  onClose,
}) => {
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const canModifyFeedback = () => {
    return feedback.createFromId === currentUser.id || 
           currentUser.userRole.isOwner;
  };

  const getCurrentUserStatus = () => {
    return feedback.feedbackUsers?.find(
      (fu: any) => fu.userId === currentUser.id
    );
  };

  const handleUserAction = async (action: string, data: any = {}) => {
    try {
      setActionLoading(action);
      await onAction(feedback.id, action, data);
    } catch (error) {
      console.error(`Error performing ${action}:`, error);
    } finally {
      setActionLoading(null);
    }
  };

  const handleReflection = async () => {
    const reflection = prompt('Enter your reflection:');
    if (reflection) {
      await handleUserAction('reflect', { reflection });
    }
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this feedback?')) {
      try {
        await onDelete(feedback.id);
      } catch (error) {
        console.error('Error deleting feedback:', error);
      }
    }
  };

  const userStatus = getCurrentUserStatus();
  const isAssignedUser = !!userStatus;

  const renderContentFields = () => {
    const fields = [
      { label: 'Situation', value: feedback.situation },
      { label: 'Behavior', value: feedback.behavior },
      { label: 'Impact', value: feedback.impact },
      { label: 'Actionable Items', value: feedback.actionable },
      { label: 'Appreciation', value: feedback.appreciation },
      { label: 'Growth Token', value: feedback.growthToken },
    ].filter(field => field.value);

    return fields.map((field, index) => (
      <ContentField key={index}>
        <FieldLabel>{field.label}</FieldLabel>
        <FieldValue>{field.value}</FieldValue>
      </ContentField>
    ));
  };

  return (
    <Container>
      <Header>
        <HeaderLeft>
          <TypeBadge type={feedback.feedback_types.name}>
            {feedback.feedback_types.name}
          </TypeBadge>
          <CreatedBy>
            {feedback.createFrom.imageUrl ? (
              <Avatar src={feedback.createFrom.imageUrl} alt="Creator" />
            ) : (
              <AvatarPlaceholder>
                {getInitials(feedback.createFrom.firstName, feedback.createFrom.lastName)}
              </AvatarPlaceholder>
            )}
            <div>
              <div>{feedback.createFrom.firstName} {feedback.createFrom.lastName}</div>
              <CreatedAt>{formatDate(feedback.createdAt)}</CreatedAt>
            </div>
          </CreatedBy>
        </HeaderLeft>

        <ActionButtons>
          {canModifyFeedback() && (
            <ActionButton variant="danger" onClick={handleDelete}>
              Delete
            </ActionButton>
          )}
        </ActionButtons>
      </Header>

      <Content>
        <ContentSection>
          <SectionTitle>Feedback Content</SectionTitle>
          {renderContentFields()}
        </ContentSection>

        <ContentSection>
          <SectionTitle>Assigned Users</SectionTitle>
          <AssignedUsers>
            {feedback.feedbackUsers?.map((fu: any) => (
              <UserCard key={fu.id}>
                <UserInfo>
                  {fu.user.imageUrl ? (
                    <Avatar src={fu.user.imageUrl} alt="User" />
                  ) : (
                    <AvatarPlaceholder>
                      {getInitials(fu.user.firstName, fu.user.lastName)}
                    </AvatarPlaceholder>
                  )}
                  <UserDetails>
                    <UserName>{fu.user.firstName} {fu.user.lastName}</UserName>
                    <UserEmail>{fu.user.email}</UserEmail>
                    {fu.reflection && (
                      <ReflectionText>"{fu.reflection}"</ReflectionText>
                    )}
                  </UserDetails>
                </UserInfo>

                <UserActions>
                  {fu.isAccept && <StatusBadge status="accepted">Accepted</StatusBadge>}
                  {fu.isDiscard && <StatusBadge status="discarded">Discarded</StatusBadge>}
                  {fu.reflection && <StatusBadge status="reflected">Reflected</StatusBadge>}
                  {fu.isShare && <StatusBadge status="shared">Shared</StatusBadge>}

                  {fu.userId === currentUser.id && (
                    <UserActionButtons>
                      <SmallButton
                        variant="accept"
                        onClick={() => handleUserAction('accept', { value: !fu.isAccept })}
                        disabled={actionLoading === 'accept'}
                      >
                        {fu.isAccept ? 'Unaccept' : 'Accept'}
                      </SmallButton>
                      <SmallButton
                        variant="discard"
                        onClick={() => handleUserAction('discard', { value: !fu.isDiscard })}
                        disabled={actionLoading === 'discard'}
                      >
                        {fu.isDiscard ? 'Undiscard' : 'Discard'}
                      </SmallButton>
                      <SmallButton
                        variant="reflect"
                        onClick={handleReflection}
                        disabled={actionLoading === 'reflect'}
                      >
                        Reflect
                      </SmallButton>
                      <SmallButton
                        variant="share"
                        onClick={() => handleUserAction('share', { value: !fu.isShare })}
                        disabled={actionLoading === 'share'}
                      >
                        {fu.isShare ? 'Unshare' : 'Share'}
                      </SmallButton>
                    </UserActionButtons>
                  )}
                </UserActions>
              </UserCard>
            ))}
          </AssignedUsers>
        </ContentSection>
      </Content>
    </Container>
  );
};
