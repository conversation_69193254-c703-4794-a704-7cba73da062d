'use client';

import React from 'react';
import styled from 'styled-components';

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const FilterSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const FilterTitle = styled.h3`
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
`;

const FilterGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Select = styled.select`
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: #007bff;
  }
`;

const CheckboxGroup = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const Checkbox = styled.input`
  width: 16px;
  height: 16px;
  cursor: pointer;
`;

const CheckboxLabel = styled.label`
  font-size: 14px;
  color: #333;
  cursor: pointer;
`;

const Input = styled.input`
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: #007bff;
  }
`;

const FilterLabel = styled.label`
  font-size: 12px;
  font-weight: 500;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const ClearButton = styled.button`
  background: none;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    border-color: #007bff;
    color: #007bff;
  }
`;

interface FeedbackFiltersProps {
  filters: {
    type: string;
    personal: boolean;
    createdBy: string;
    assignedTo: string;
    organizationId: string;
    departmentId: string;
  };
  feedbackTypes: any[];
  onFiltersChange: (filters: any) => void;
  userRole: any;
}

export const FeedbackFilters: React.FC<FeedbackFiltersProps> = ({
  filters,
  feedbackTypes,
  onFiltersChange,
  userRole,
}) => {
  const handleFilterChange = (key: string, value: any) => {
    onFiltersChange({ [key]: value });
  };

  const handleClearFilters = () => {
    onFiltersChange({
      type: '',
      personal: true,
      createdBy: '',
      assignedTo: '',
      organizationId: '',
      departmentId: '',
    });
  };

  const canViewAllFeedback = () => {
    return userRole.isOwner || userRole.isAdmin;
  };

  return (
    <Container>
      <FilterSection>
        <FilterTitle>Filters</FilterTitle>
        
        <FilterGroup>
          <FilterLabel>Feedback Type</FilterLabel>
          <Select
            value={filters.type}
            onChange={(e) => handleFilterChange('type', e.target.value)}
          >
            <option value="">All Types</option>
            {feedbackTypes.map((type) => (
              <option key={type.id} value={type.name}>
                {type.name.charAt(0).toUpperCase() + type.name.slice(1)}
              </option>
            ))}
          </Select>
        </FilterGroup>

        <FilterGroup>
          <CheckboxGroup>
            <Checkbox
              type="checkbox"
              id="personal"
              checked={filters.personal}
              onChange={(e) => handleFilterChange('personal', e.target.checked)}
            />
            <CheckboxLabel htmlFor="personal">
              Personal Feedback Only
            </CheckboxLabel>
          </CheckboxGroup>
        </FilterGroup>

        {canViewAllFeedback() && !filters.personal && (
          <>
            <FilterGroup>
              <FilterLabel>Created By (User ID)</FilterLabel>
              <Input
                type="number"
                placeholder="Enter user ID"
                value={filters.createdBy}
                onChange={(e) => handleFilterChange('createdBy', e.target.value)}
              />
            </FilterGroup>

            <FilterGroup>
              <FilterLabel>Assigned To (User ID)</FilterLabel>
              <Input
                type="number"
                placeholder="Enter user ID"
                value={filters.assignedTo}
                onChange={(e) => handleFilterChange('assignedTo', e.target.value)}
              />
            </FilterGroup>

            {userRole.isOwner && (
              <FilterGroup>
                <FilterLabel>Organization ID</FilterLabel>
                <Input
                  type="number"
                  placeholder="Enter organization ID"
                  value={filters.organizationId}
                  onChange={(e) => handleFilterChange('organizationId', e.target.value)}
                />
              </FilterGroup>
            )}

            <FilterGroup>
              <FilterLabel>Department ID</FilterLabel>
              <Input
                type="number"
                placeholder="Enter department ID"
                value={filters.departmentId}
                onChange={(e) => handleFilterChange('departmentId', e.target.value)}
              />
            </FilterGroup>
          </>
        )}

        <ClearButton onClick={handleClearFilters}>
          Clear Filters
        </ClearButton>
      </FilterSection>

      <FilterSection>
        <FilterTitle>Quick Filters</FilterTitle>
        
        <FilterGroup>
          <CheckboxGroup>
            <Checkbox
              type="radio"
              name="quickFilter"
              checked={filters.personal && !filters.type}
              onChange={() => onFiltersChange({
                personal: true,
                type: '',
                createdBy: '',
                assignedTo: '',
                organizationId: '',
                departmentId: '',
              })}
            />
            <CheckboxLabel>All Personal</CheckboxLabel>
          </CheckboxGroup>
          
          <CheckboxGroup>
            <Checkbox
              type="radio"
              name="quickFilter"
              checked={filters.personal && filters.type === 'private'}
              onChange={() => onFiltersChange({
                personal: true,
                type: 'private',
                createdBy: '',
                assignedTo: '',
                organizationId: '',
                departmentId: '',
              })}
            />
            <CheckboxLabel>Private Feedback</CheckboxLabel>
          </CheckboxGroup>
          
          <CheckboxGroup>
            <Checkbox
              type="radio"
              name="quickFilter"
              checked={filters.personal && filters.type === 'task'}
              onChange={() => onFiltersChange({
                personal: true,
                type: 'task',
                createdBy: '',
                assignedTo: '',
                organizationId: '',
                departmentId: '',
              })}
            />
            <CheckboxLabel>Task Feedback</CheckboxLabel>
          </CheckboxGroup>

          {canViewAllFeedback() && (
            <>
              <CheckboxGroup>
                <Checkbox
                  type="radio"
                  name="quickFilter"
                  checked={!filters.personal && filters.type === 'department'}
                  onChange={() => onFiltersChange({
                    personal: false,
                    type: 'department',
                    createdBy: '',
                    assignedTo: '',
                    organizationId: '',
                    departmentId: '',
                  })}
                />
                <CheckboxLabel>Department Feedback</CheckboxLabel>
              </CheckboxGroup>

              {userRole.isOwner && (
                <CheckboxGroup>
                  <Checkbox
                    type="radio"
                    name="quickFilter"
                    checked={!filters.personal && filters.type === 'organization'}
                    onChange={() => onFiltersChange({
                      personal: false,
                      type: 'organization',
                      createdBy: '',
                      assignedTo: '',
                      organizationId: '',
                      departmentId: '',
                    })}
                  />
                  <CheckboxLabel>Organization Feedback</CheckboxLabel>
                </CheckboxGroup>
              )}
            </>
          )}
        </FilterGroup>
      </FilterSection>
    </Container>
  );
};
