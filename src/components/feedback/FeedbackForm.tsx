'use client';

import React, { useState } from 'react';
import styled from 'styled-components';

const Container = styled.div`
  padding: 24px;
`;

const Title = styled.h2`
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 24px 0;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Label = styled.label`
  font-size: 14px;
  font-weight: 500;
  color: #333;
`;

const RequiredLabel = styled(Label)`
  &::after {
    content: ' *';
    color: #dc3545;
  }
`;

const Select = styled.select`
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  background: white;

  &:focus {
    outline: none;
    border-color: #007bff;
  }
`;

const Input = styled.input`
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: #007bff;
  }
`;

const TextArea = styled.textarea`
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  min-height: 100px;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: #007bff;
  }
`;

const UserSelector = styled.div`
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 12px;
  max-height: 200px;
  overflow-y: auto;
`;

const UserItem = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;

  &:hover {
    background: #f8f9fa;
  }
`;

const Checkbox = styled.input`
  width: 16px;
  height: 16px;
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
`;

const Avatar = styled.img`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
`;

const AvatarPlaceholder = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
`;

const UserDetails = styled.div`
  display: flex;
  flex-direction: column;
`;

const UserName = styled.span`
  font-size: 14px;
  font-weight: 500;
  color: #333;
`;

const UserEmail = styled.span`
  font-size: 12px;
  color: #666;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
`;

const Button = styled.button<{ variant?: 'primary' | 'secondary' }>`
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  ${props => props.variant === 'primary' ? `
    background: #007bff;
    color: white;
    border: none;

    &:hover:not(:disabled) {
      background: #0056b3;
    }

    &:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
  ` : `
    background: white;
    color: #666;
    border: 1px solid #ddd;

    &:hover {
      border-color: #007bff;
      color: #007bff;
    }
  `}
`;

const ErrorMessage = styled.div`
  background: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 8px;
  font-size: 14px;
  border: 1px solid #f5c6cb;
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  font-size: 14px;
  color: #666;
`;

interface FeedbackFormProps {
  feedbackTypes: any[];
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  userRole: any;
}

export const FeedbackForm: React.FC<FeedbackFormProps> = ({
  feedbackTypes,
  onSubmit,
  onCancel,
  userRole,
}) => {
  const [formData, setFormData] = useState({
    type: '',
    situation: '',
    behavior: '',
    impact: '',
    actionable: '',
    appreciation: '',
    growthToken: '',
    assignedUserIds: [] as number[],
    taskId: '',
    departmentId: '',
    organizationId: '',
  });
  const [users, setUsers] = useState([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [error, setError] = useState('');
  const [submitting, setSubmitting] = useState(false);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Load users when type is selected
    if (field === 'type' && value) {
      loadUsers(value);
    }
  };

  const loadUsers = async (type: string) => {
    try {
      setLoadingUsers(true);
      // This would typically load users based on the feedback type and user permissions
      // For now, we'll use a placeholder API call
      const response = await fetch('/api/v1/users'); // You'll need to implement this
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users || []);
      }
    } catch (error) {
      console.error('Error loading users:', error);
    } finally {
      setLoadingUsers(false);
    }
  };

  const handleUserToggle = (userId: number) => {
    setFormData(prev => ({
      ...prev,
      assignedUserIds: prev.assignedUserIds.includes(userId)
        ? prev.assignedUserIds.filter(id => id !== userId)
        : [...prev.assignedUserIds, userId]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.type || formData.assignedUserIds.length === 0) {
      setError('Please select a feedback type and at least one user to assign to.');
      return;
    }

    try {
      setSubmitting(true);
      setError('');
      
      const submitData = {
        ...formData,
        taskId: formData.taskId ? parseInt(formData.taskId) : undefined,
        departmentId: formData.departmentId ? parseInt(formData.departmentId) : undefined,
        organizationId: formData.organizationId ? parseInt(formData.organizationId) : undefined,
      };

      await onSubmit(submitData);
    } catch (error: any) {
      setError(error.message || 'Failed to create feedback');
    } finally {
      setSubmitting(false);
    }
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase();
  };

  return (
    <Container>
      <Title>Create New Feedback</Title>
      
      {error && <ErrorMessage>{error}</ErrorMessage>}
      
      <Form onSubmit={handleSubmit}>
        <FormGroup>
          <RequiredLabel>Feedback Type</RequiredLabel>
          <Select
            value={formData.type}
            onChange={(e) => handleInputChange('type', e.target.value)}
            required
          >
            <option value="">Select feedback type</option>
            {feedbackTypes.map((type) => (
              <option key={type.id} value={type.name}>
                {type.name.charAt(0).toUpperCase() + type.name.slice(1)} - {type.description}
              </option>
            ))}
          </Select>
        </FormGroup>

        {formData.type === 'task' && (
          <FormGroup>
            <Label>Task ID</Label>
            <Input
              type="number"
              placeholder="Enter task ID"
              value={formData.taskId}
              onChange={(e) => handleInputChange('taskId', e.target.value)}
            />
          </FormGroup>
        )}

        {formData.type === 'department' && (
          <FormGroup>
            <Label>Department ID</Label>
            <Input
              type="number"
              placeholder="Enter department ID"
              value={formData.departmentId}
              onChange={(e) => handleInputChange('departmentId', e.target.value)}
            />
          </FormGroup>
        )}

        {formData.type === 'organization' && (
          <FormGroup>
            <Label>Organization ID</Label>
            <Input
              type="number"
              placeholder="Enter organization ID"
              value={formData.organizationId}
              onChange={(e) => handleInputChange('organizationId', e.target.value)}
            />
          </FormGroup>
        )}

        <FormGroup>
          <Label>Situation</Label>
          <TextArea
            placeholder="Describe the situation..."
            value={formData.situation}
            onChange={(e) => handleInputChange('situation', e.target.value)}
          />
        </FormGroup>

        <FormGroup>
          <Label>Behavior</Label>
          <TextArea
            placeholder="Describe the observed behavior..."
            value={formData.behavior}
            onChange={(e) => handleInputChange('behavior', e.target.value)}
          />
        </FormGroup>

        <FormGroup>
          <Label>Impact</Label>
          <TextArea
            placeholder="Describe the impact..."
            value={formData.impact}
            onChange={(e) => handleInputChange('impact', e.target.value)}
          />
        </FormGroup>

        <FormGroup>
          <Label>Actionable Items</Label>
          <TextArea
            placeholder="Suggest actionable improvements..."
            value={formData.actionable}
            onChange={(e) => handleInputChange('actionable', e.target.value)}
          />
        </FormGroup>

        <FormGroup>
          <Label>Appreciation</Label>
          <TextArea
            placeholder="Express appreciation..."
            value={formData.appreciation}
            onChange={(e) => handleInputChange('appreciation', e.target.value)}
          />
        </FormGroup>

        <FormGroup>
          <Label>Growth Token</Label>
          <Input
            placeholder="Enter growth token value..."
            value={formData.growthToken}
            onChange={(e) => handleInputChange('growthToken', e.target.value)}
          />
        </FormGroup>

        {formData.type && (
          <FormGroup>
            <RequiredLabel>Assign to Users</RequiredLabel>
            {loadingUsers ? (
              <LoadingSpinner>Loading users...</LoadingSpinner>
            ) : (
              <UserSelector>
                {users.map((user: any) => (
                  <UserItem key={user.id} onClick={() => handleUserToggle(user.id)}>
                    <Checkbox
                      type="checkbox"
                      checked={formData.assignedUserIds.includes(user.id)}
                      onChange={() => handleUserToggle(user.id)}
                    />
                    <UserInfo>
                      {user.imageUrl ? (
                        <Avatar src={user.imageUrl} alt="User" />
                      ) : (
                        <AvatarPlaceholder>
                          {getInitials(user.firstName, user.lastName)}
                        </AvatarPlaceholder>
                      )}
                      <UserDetails>
                        <UserName>{user.firstName} {user.lastName}</UserName>
                        <UserEmail>{user.email}</UserEmail>
                      </UserDetails>
                    </UserInfo>
                  </UserItem>
                ))}
                {users.length === 0 && (
                  <div style={{ padding: '20px', textAlign: 'center', color: '#666' }}>
                    No users available for this feedback type
                  </div>
                )}
              </UserSelector>
            )}
          </FormGroup>
        )}

        <ButtonGroup>
          <Button type="button" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" variant="primary" disabled={submitting}>
            {submitting ? 'Creating...' : 'Create Feedback'}
          </Button>
        </ButtonGroup>
      </Form>
    </Container>
  );
};
