'use client';

import React from 'react';
import styled from 'styled-components';

const Card = styled.div`
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.1);
  }
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
`;

const TypeBadge = styled.span<{ type: string }>`
  background: ${props => {
    switch (props.type) {
      case 'private': return '#e3f2fd';
      case 'task': return '#f3e5f5';
      case 'department': return '#e8f5e8';
      case 'organization': return '#fff3e0';
      default: return '#f5f5f5';
    }
  }};
  color: ${props => {
    switch (props.type) {
      case 'private': return '#1976d2';
      case 'task': return '#7b1fa2';
      case 'department': return '#388e3c';
      case 'organization': return '#f57c00';
      default: return '#666';
    }
  }};
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
`;

const CreatedBy = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
`;

const Avatar = styled.img`
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
`;

const AvatarPlaceholder = styled.div`
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
`;

const Content = styled.div`
  margin-bottom: 16px;
`;

const ContentField = styled.div`
  margin-bottom: 12px;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const FieldLabel = styled.div`
  font-size: 12px;
  font-weight: 500;
  color: #666;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const FieldValue = styled.div`
  font-size: 14px;
  color: #333;
  line-height: 1.4;
`;

const Footer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
`;

const AssignedUsers = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const AssignedLabel = styled.span`
  font-size: 12px;
  color: #666;
`;

const UserAvatars = styled.div`
  display: flex;
  gap: -8px;
`;

const UserAvatar = styled.img`
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid white;
  object-fit: cover;
`;

const UserAvatarPlaceholder = styled.div`
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid white;
  background: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 500;
`;

const CreatedAt = styled.div`
  font-size: 12px;
  color: #999;
`;

const StatusIndicators = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 8px;
`;

const StatusBadge = styled.span<{ status: 'accepted' | 'discarded' | 'reflected' | 'shared' }>`
  background: ${props => {
    switch (props.status) {
      case 'accepted': return '#d4edda';
      case 'discarded': return '#f8d7da';
      case 'reflected': return '#d1ecf1';
      case 'shared': return '#fff3cd';
      default: return '#f5f5f5';
    }
  }};
  color: ${props => {
    switch (props.status) {
      case 'accepted': return '#155724';
      case 'discarded': return '#721c24';
      case 'reflected': return '#0c5460';
      case 'shared': return '#856404';
      default: return '#666';
    }
  }};
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  text-transform: capitalize;
`;

interface FeedbackCardProps {
  feedback: any;
  currentUser: any;
  onClick: () => void;
  onAction: (feedbackId: number, action: string, data: any) => Promise<void>;
}

export const FeedbackCard: React.FC<FeedbackCardProps> = ({
  feedback,
  currentUser,
  onClick,
  onAction,
}) => {
  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const getCurrentUserStatus = () => {
    const userFeedback = feedback.feedbackUsers?.find(
      (fu: any) => fu.userId === currentUser.id
    );
    return userFeedback;
  };

  const userStatus = getCurrentUserStatus();

  const renderContentPreview = () => {
    const fields = [
      { label: 'Situation', value: feedback.situation },
      { label: 'Behavior', value: feedback.behavior },
      { label: 'Impact', value: feedback.impact },
      { label: 'Actionable', value: feedback.actionable },
      { label: 'Appreciation', value: feedback.appreciation },
    ].filter(field => field.value);

    return fields.slice(0, 2).map((field, index) => (
      <ContentField key={index}>
        <FieldLabel>{field.label}</FieldLabel>
        <FieldValue>
          {field.value.length > 100 
            ? `${field.value.substring(0, 100)}...` 
            : field.value
          }
        </FieldValue>
      </ContentField>
    ));
  };

  return (
    <Card onClick={onClick}>
      <Header>
        <TypeBadge type={feedback.feedback_types.name}>
          {feedback.feedback_types.name}
        </TypeBadge>
        <CreatedBy>
          {feedback.createFrom.imageUrl ? (
            <Avatar src={feedback.createFrom.imageUrl} alt="Creator" />
          ) : (
            <AvatarPlaceholder>
              {getInitials(feedback.createFrom.firstName, feedback.createFrom.lastName)}
            </AvatarPlaceholder>
          )}
          {feedback.createFrom.firstName} {feedback.createFrom.lastName}
        </CreatedBy>
      </Header>

      <Content>
        {renderContentPreview()}
      </Content>

      <Footer>
        <AssignedUsers>
          <AssignedLabel>Assigned to:</AssignedLabel>
          <UserAvatars>
            {feedback.feedbackUsers?.slice(0, 3).map((fu: any) => (
              fu.user.imageUrl ? (
                <UserAvatar key={fu.id} src={fu.user.imageUrl} alt="User" />
              ) : (
                <UserAvatarPlaceholder key={fu.id}>
                  {getInitials(fu.user.firstName, fu.user.lastName)}
                </UserAvatarPlaceholder>
              )
            ))}
            {feedback.feedbackUsers?.length > 3 && (
              <UserAvatarPlaceholder>
                +{feedback.feedbackUsers.length - 3}
              </UserAvatarPlaceholder>
            )}
          </UserAvatars>
        </AssignedUsers>
        
        <CreatedAt>
          {formatDate(feedback.createdAt)}
        </CreatedAt>
      </Footer>

      {userStatus && (
        <StatusIndicators>
          {userStatus.isAccept && <StatusBadge status="accepted">Accepted</StatusBadge>}
          {userStatus.isDiscard && <StatusBadge status="discarded">Discarded</StatusBadge>}
          {userStatus.reflection && <StatusBadge status="reflected">Reflected</StatusBadge>}
          {userStatus.isShare && <StatusBadge status="shared">Shared</StatusBadge>}
        </StatusIndicators>
      )}
    </Card>
  );
};
