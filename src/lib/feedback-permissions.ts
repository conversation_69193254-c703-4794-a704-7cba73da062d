import { prisma } from '@/lib/prisma';

/**
 * Helper function to check if user can assign feedback to specific users
 */
export async function canAssignToUsers(
  assignedUserIds: number[],
  organizationIds: number[] = [],
  departmentIds: number[] = []
): Promise<boolean> {
  if (assignedUserIds.length === 0) return true;

  // If organization IDs are provided, check if users belong to those organizations
  if (organizationIds.length > 0) {
    const usersInOrganizations = await prisma.user.findMany({
      where: {
        id: { in: assignedUserIds },
        departmentMembers: {
          some: {
            department: {
              organizationId: { in: organizationIds },
            },
          },
        },
      },
      select: { id: true },
    });

    return usersInOrganizations.length === assignedUserIds.length;
  }

  // If department IDs are provided, check if users belong to those departments
  if (departmentIds.length > 0) {
    const usersInDepartments = await prisma.user.findMany({
      where: {
        id: { in: assignedUserIds },
        departmentMembers: {
          some: {
            departmentId: { in: departmentIds },
          },
        },
      },
      select: { id: true },
    });

    return usersInDepartments.length === assignedUserIds.length;
  }

  return false;
}

/**
 * Helper function to check if user is a department leader
 */
export async function isUserDepartmentLeader(userId: number): Promise<{
  isLeader: boolean;
  departmentIds: number[];
}> {
  const departmentMemberships = await prisma.departmentMember.findMany({
    where: {
      userId,
      isLeader: true,
    },
    select: {
      departmentId: true,
    },
  });

  return {
    isLeader: departmentMemberships.length > 0,
    departmentIds: departmentMemberships.map(dm => dm.departmentId),
  };
}

/**
 * Helper function to check if user can edit/delete feedback
 */
export async function canModifyFeedback(
  userId: number,
  feedback: any,
  auth: any
): Promise<boolean> {
  // Creator can always modify
  if (feedback.createFromId === userId) {
    return true;
  }

  // Organization owner can modify feedback in their organization
  if (auth.isOwner && feedback.organization_id) {
    const userPrivileges = await getFeedbackUserOrganizationAdminPrivileges(userId);
    return userPrivileges.ownedOrganizations.includes(feedback.organization_id);
  }

  return false;
}

/**
 * Helper function to get user's organization admin privileges for feedback
 */
export async function getFeedbackUserOrganizationAdminPrivileges(userId: number) {
  // Get user's role information
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: { userRole: true },
  });

  if (!user) {
    return {
      ownedOrganizations: [],
      adminOrganizations: [],
      allAdminOrganizations: [],
    };
  }

  let ownedOrganizations: number[] = [];
  let adminOrganizations: number[] = [];

  // If user is owner, get owned organizations
  if (user.userRole.isOwner) {
    const owned = await prisma.organization.findMany({
      where: { ownerUserId: userId },
      select: { id: true },
    });
    ownedOrganizations = owned.map(org => org.id);
  }

  // If user is admin, get admin organizations
  if (user.userRole.isAdmin) {
    const adminRoles = await prisma.organizationAdmin.findMany({
      where: { userId },
      select: { organizationId: true },
    });
    adminOrganizations = adminRoles.map(role => role.organizationId);
  }

  const allAdminOrganizations = [...ownedOrganizations, ...adminOrganizations];

  return {
    ownedOrganizations,
    adminOrganizations,
    allAdminOrganizations,
  };
}
