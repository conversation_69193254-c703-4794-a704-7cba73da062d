generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model UserRole {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  isOwner     Boolean  @default(false) @map("is_owner")
  isAdmin     <PERSON>  @default(false) @map("is_admin")
  isMember    Boolean  @default(false) @map("is_member")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  isBot       Boolean  @default(false) @map("is_bot")
  users       User[]

  @@map("user_roles")
}

model User {
  id                 Int                 @id @default(autoincrement())
  email              String              @unique
  passwordHash       String              @map("password_hash")
  firstName          String              @map("first_name")
  lastName           String              @map("last_name")
  phone              String?
  imageUrl           String?             @map("image_url")
  userRoleId         Int                 @map("user_role_id")
  createdAt          DateTime            @default(now()) @map("created_at")
  updatedAt          DateTime            @updatedAt @map("updated_at")
  deletedAt          DateTime?           @map("deleted_at")
  isAdmin            Boolean             @default(false) @map("is_admin")
  isMember           Boolean             @default(false) @map("is_member")
  isOwner            Boolean             @default(false) @map("is_owner")
  isBot              Boolean             @default(false) @map("is_bot")
  assistantChatUsers AssistantChatUser[]
  chatMessages       ChatMessage[]
  chatUsers          ChatUser[]
  departmentMembers  DepartmentMember[]
  assignedFeedbacks  FeedbackUser[]      @relation("FeedbackAssignedTo")
  createdFeedbacks   Feedback[]          @relation("FeedbackCreatedBy")
  messageReads       MessageRead[]
  assignedOrgAdmins  OrganizationAdmin[] @relation("OrganizationAdminAssignedBy")
  organizationAdmins OrganizationAdmin[]
  ownedOrganizations Organization[]
  pointTransactions  PointTransaction[]
  assignedByMe       TaskAssignment[]    @relation("AssignedBy")
  taskAssignments    TaskAssignment[]
  taskProgresses     TaskProgress[]
  createdTasks       Task[]              @relation("CreatedByUser")
  userNotifications  UserNotification[]
  userRole           UserRole            @relation(fields: [userRoleId], references: [id])

  @@map("users")
}

model Organization {
  id                 Int                 @id @default(autoincrement())
  name               String
  description        String?
  imageUrl           String?             @map("image_url")
  ownerUserId        Int                 @map("owner_user_id")
  createdAt          DateTime            @default(now()) @map("created_at")
  updatedAt          DateTime            @updatedAt @map("updated_at")
  chats              Chat[]
  departments        Department[]
  feedbacks          Feedback[]
  organizationAdmins OrganizationAdmin[]
  owner              User                @relation(fields: [ownerUserId], references: [id])
  tasks              Task[]

  @@map("organizations")
}

model Department {
  id             Int                @id @default(autoincrement())
  name           String
  description    String?
  organizationId Int                @map("organization_id")
  createdAt      DateTime           @default(now()) @map("created_at")
  updatedAt      DateTime           @updatedAt @map("updated_at")
  chats          Chat[]
  members        DepartmentMember[]
  organization   Organization       @relation(fields: [organizationId], references: [id])
  feedbacks      Feedback[]
  tasks          Task[]

  @@map("departments")
}

model DepartmentMember {
  id           Int        @id @default(autoincrement())
  userId       Int        @map("user_id")
  departmentId Int        @map("department_id")
  joinedAt     DateTime   @map("joined_at")
  createdAt    DateTime   @default(now()) @map("created_at")
  updatedAt    DateTime   @updatedAt @map("updated_at")
  isLeader     Boolean    @default(false) @map("is_leader")
  department   Department @relation(fields: [departmentId], references: [id])
  user         User       @relation(fields: [userId], references: [id])

  @@map("department_members")
}

model TaskStatus {
  id                    Int                    @id @default(autoincrement())
  name                  String
  displayName           String                 @map("display_name")
  color                 String
  description           String?
  index                 Int                    @default(0) @map("index")
  isMemberDisplay       Boolean                @default(true) @map("is_member_display")
  createdAt             DateTime               @default(now()) @map("created_at")
  updatedAt             DateTime               @updatedAt @map("updated_at")
  fromStatusTransitions TaskStatusTransition[] @relation("FromStatus")
  toStatusTransitions   TaskStatusTransition[] @relation("ToStatus")
  tasks                 Task[]

  @@map("task_status")
}

model TaskStatusTransition {
  id           Int        @id @default(autoincrement())
  fromStatusId Int        @map("from_status_id")
  toStatusId   Int        @map("to_status_id")
  allowOwner   Boolean    @default(true) @map("allow_owner")
  allowAdmin   Boolean    @default(false) @map("allow_admin")
  allowMember  Boolean    @default(false) @map("allow_member")
  description  String?
  createdAt    DateTime   @default(now()) @map("created_at")
  updatedAt    DateTime   @updatedAt @map("updated_at")
  fromStatus   TaskStatus @relation("FromStatus", fields: [fromStatusId], references: [id])
  toStatus     TaskStatus @relation("ToStatus", fields: [toStatusId], references: [id])

  @@unique([fromStatusId, toStatusId])
  @@map("task_status_transitions")
}

model Task {
  id                Int                @id @default(autoincrement())
  taskTitle         String             @map("task_title")
  taskDescription   String?            @map("task_description")
  createdByUserId   Int                @map("created_by_user_id")
  statusId          Int                @map("status_id")
  organizationId    Int                @map("organization_id")
  departmentId      Int                @map("department_id")
  points            Int?
  isClaimPoint      Boolean            @default(false) @map("is_claim_point")
  dueDate           DateTime?          @map("due_date")
  createdAt         DateTime           @default(now()) @map("created_at")
  updatedAt         DateTime           @updatedAt @map("updated_at")
  chats             Chat[]
  feedbacks         Feedback[]
  pointTransactions PointTransaction[]
  taskAssignments   TaskAssignment[]
  taskProgresses    TaskProgress[]
  createdByUser     User               @relation("CreatedByUser", fields: [createdByUserId], references: [id])
  department        Department         @relation(fields: [departmentId], references: [id])
  organization      Organization       @relation(fields: [organizationId], references: [id])
  status            TaskStatus         @relation(fields: [statusId], references: [id])

  @@map("tasks")
}

model TaskAssignment {
  id             Int      @id @default(autoincrement())
  taskId         Int      @map("task_id")
  userId         Int      @map("user_id")
  assignedAt     DateTime @default(now()) @map("assigned_at")
  assignedBy     Int?     @map("assigned_by")
  isLeader       Boolean  @default(false) @map("is_leader")
  isActive       Boolean  @default(true) @map("is_active")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")
  assignedByUser User?    @relation("AssignedBy", fields: [assignedBy], references: [id])
  task           Task     @relation(fields: [taskId], references: [id], onDelete: Cascade)
  user           User     @relation(fields: [userId], references: [id])

  @@unique([taskId, userId])
  @@map("task_assignments")
}

model TaskProgressType {
  id             Int            @id @default(autoincrement())
  name           String
  displayName    String         @map("display_name")
  color          String
  description    String?
  createdAt      DateTime       @default(now()) @map("created_at")
  updatedAt      DateTime       @updatedAt @map("updated_at")
  isAdmin        Boolean        @default(true) @map("is_admin")
  isMember       Boolean        @default(true) @map("is_member")
  isOwner        Boolean        @default(true) @map("is_owner")
  taskProgresses TaskProgress[]

  @@map("task_progress_types")
}

model TaskProgress {
  id                  Int              @id @default(autoincrement())
  taskId              Int              @map("task_id")
  updatedByUserId     Int              @map("updated_by_user_id")
  progressTypeId      Int              @map("progress_type_id")
  progressDescription String           @map("progress_description")
  createdAt           DateTime         @default(now()) @map("created_at")
  updatedAt           DateTime         @updatedAt @map("updated_at")
  progressType        TaskProgressType @relation(fields: [progressTypeId], references: [id])
  task                Task             @relation(fields: [taskId], references: [id])
  updatedByUser       User             @relation(fields: [updatedByUserId], references: [id])

  @@map("task_progress")
}

model PointTransaction {
  id          Int      @id @default(autoincrement())
  userId      Int      @map("user_id")
  taskId      Int      @map("task_id")
  pointAmount Int      @map("point_amount")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  task        Task     @relation(fields: [taskId], references: [id])
  user        User     @relation(fields: [userId], references: [id])

  @@map("point_transactions")
}

model NotificationType {
  id            Int            @id @default(autoincrement())
  name          String         @unique
  displayName   String         @map("display_name")
  description   String?
  template      String
  color         String?
  isActive      Boolean        @default(true) @map("is_active")
  createdAt     DateTime       @default(now()) @map("created_at")
  updatedAt     DateTime       @updatedAt @map("updated_at")
  notifications Notification[]

  @@map("notification_types")
}

model Notification {
  id                Int                @id @default(autoincrement())
  typeId            Int                @map("type_id")
  title             String
  content           String
  data              Json?
  entityType        String?            @map("entity_type")
  entityId          Int?               @map("entity_id")
  createdAt         DateTime           @default(now()) @map("created_at")
  updatedAt         DateTime           @updatedAt @map("updated_at")
  type              NotificationType   @relation(fields: [typeId], references: [id])
  userNotifications UserNotification[]

  @@map("notifications")
}

model UserNotification {
  id             Int          @id @default(autoincrement())
  userId         Int          @map("user_id")
  notificationId Int          @map("notification_id")
  isRead         Boolean      @default(false) @map("is_read")
  readAt         DateTime?    @map("read_at")
  isArchived     Boolean      @default(false) @map("is_archived")
  archivedAt     DateTime?    @map("archived_at")
  createdAt      DateTime     @default(now()) @map("created_at")
  updatedAt      DateTime     @updatedAt @map("updated_at")
  notification   Notification @relation(fields: [notificationId], references: [id])
  user           User         @relation(fields: [userId], references: [id])

  @@unique([userId, notificationId])
  @@map("user_notifications")
}

model Chat {
  id                 Int                 @id @default(autoincrement())
  name               String?
  chatType           ChatType            @map("chat_type")
  organizationId     Int?                @map("organization_id")
  departmentId       Int?                @map("department_id")
  taskId             Int?                @map("task_id")
  createdAt          DateTime            @default(now()) @map("created_at")
  updatedAt          DateTime            @updatedAt @map("updated_at")
  isActive           Boolean             @default(true) @map("is_active")
  lastMessageAt      DateTime?           @map("last_message_at")
  botDuration        Int?                @map("bot_duration")
  isBot              Boolean             @default(false) @map("is_bot")
  assistantChatUsers AssistantChatUser[]
  messages           ChatMessage[]
  chatUsers          ChatUser[]
  department         Department?         @relation(fields: [departmentId], references: [id])
  organization       Organization?       @relation(fields: [organizationId], references: [id])
  task               Task?               @relation(fields: [taskId], references: [id])

  @@map("chats")
}

model ChatUser {
  id        Int      @id @default(autoincrement())
  chatId    Int      @map("chat_id")
  userId    Int      @map("user_id")
  joinedAt  DateTime @default(now()) @map("joined_at")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  isAdmin   Boolean  @default(false) @map("is_admin")
  chat      Chat     @relation(fields: [chatId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id])

  @@unique([chatId, userId])
  @@map("chat_users")
}

model ChatMessage {
  id            Int           @id @default(autoincrement())
  chatId        Int           @map("chat_id")
  userId        Int           @map("user_id")
  content       String
  createdAt     DateTime      @default(now()) @map("created_at")
  updatedAt     DateTime      @updatedAt @map("updated_at")
  messageStatus MessageStatus @default(SENDING) @map("message_status")
  messageType   MessageType   @default(TEXT) @map("message_type")
  chat          Chat          @relation(fields: [chatId], references: [id], onDelete: Cascade)
  user          User          @relation(fields: [userId], references: [id])
  messageReads  MessageRead[]

  @@map("chat_messages")
}

model MessageRead {
  id        Int         @id @default(autoincrement())
  messageId Int         @map("message_id")
  userId    Int         @map("user_id")
  readAt    DateTime    @default(now()) @map("read_at")
  createdAt DateTime    @default(now()) @map("created_at")
  updatedAt DateTime    @updatedAt @map("updated_at")
  message   ChatMessage @relation(fields: [messageId], references: [id], onDelete: Cascade)
  user      User        @relation(fields: [userId], references: [id])

  @@unique([messageId, userId])
  @@index([userId, readAt])
  @@index([messageId])
  @@map("message_reads")
}

model OrganizationAdmin {
  id             Int          @id @default(autoincrement())
  userId         Int          @map("user_id")
  organizationId Int          @map("organization_id")
  assignedAt     DateTime     @default(now()) @map("assigned_at")
  assignedBy     Int?         @map("assigned_by")
  isActive       Boolean      @default(true) @map("is_active")
  createdAt      DateTime     @default(now()) @map("created_at")
  updatedAt      DateTime     @updatedAt @map("updated_at")
  assignedByUser User?        @relation("OrganizationAdminAssignedBy", fields: [assignedBy], references: [id])
  organization   Organization @relation(fields: [organizationId], references: [id])
  user           User         @relation(fields: [userId], references: [id])

  @@unique([userId, organizationId])
  @@map("organization_admins")
}

model AssistantMessageType {
  id                Int                @id @default(autoincrement())
  name              String             @unique
  description       String?
  createdAt         DateTime           @default(now()) @map("created_at")
  updatedAt         DateTime           @updatedAt @map("updated_at")
  assistantMessages AssistantMessage[]

  @@map("assistant_message_types")
}

model AssistantChatUser {
  id                Int                @id @default(autoincrement())
  name              String?
  chatId            Int                @map("chat_id")
  userId            Int                @map("user_id")
  isActive          Boolean            @default(true) @map("is_active")
  createdAt         DateTime           @default(now()) @map("created_at")
  updatedAt         DateTime           @updatedAt @map("updated_at")
  lastInteractionAt DateTime?          @map("last_interaction_at")
  sessionId         String?            @map("session_id")
  chat              Chat               @relation(fields: [chatId], references: [id], onDelete: Cascade)
  user              User               @relation(fields: [userId], references: [id])
  assistantMessages AssistantMessage[]

  @@index([userId, chatId])
  @@index([userId, isActive])
  @@index([chatId, isActive])
  @@index([sessionId])
  @@index([createdAt])
  @@index([lastInteractionAt])
  @@map("assistant_chat_users")
}

model Feedback {
  id              Int            @id @default(autoincrement())
  type_id         Int
  createFromId    Int            @map("create_from_id")
  situation       String?
  behavior        String?
  impact          String?
  actionable      String?
  appreciation    String?
  growthToken     String?        @map("growth_token")
  createdAt       DateTime       @default(now()) @map("created_at")
  updatedAt       DateTime       @updatedAt @map("updated_at")
  department_id   Int?
  organization_id Int?
  task_id         Int?
  feedbackUsers   FeedbackUser[]
  createFrom      User           @relation("FeedbackCreatedBy", fields: [createFromId], references: [id])
  departments     Department?    @relation(fields: [department_id], references: [id])
  organizations   Organization?  @relation(fields: [organization_id], references: [id])
  tasks           Task?          @relation(fields: [task_id], references: [id])
  feedback_types  feedback_types @relation(fields: [type_id], references: [id])

  @@index([createFromId])
  @@index([createdAt])
  @@index([department_id])
  @@index([organization_id])
  @@index([task_id])
  @@index([type_id])
  @@map("feedbacks")
}

model FeedbackUser {
  id         Int      @id @default(autoincrement())
  feedbackId Int      @map("feedback_id")
  userId     Int      @map("user_id")
  isAccept   Boolean? @map("is_accept")
  isDiscard  Boolean? @map("is_discard")
  reflection String?
  isShare    Boolean  @default(false) @map("is_share")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")
  feedback   Feedback @relation(fields: [feedbackId], references: [id], onDelete: Cascade)
  user       User     @relation("FeedbackAssignedTo", fields: [userId], references: [id])

  @@unique([feedbackId, userId])
  @@index([userId])
  @@index([feedbackId])
  @@index([isAccept])
  @@index([isDiscard])
  @@index([isShare])
  @@map("feedback_users")
}

model AssistantMessage {
  id                     Int                  @id @default(autoincrement())
  assistantChatUserId    Int                  @map("assistant_chat_user_id")
  content                String
  metadata               Json?
  completionTokens       Int?                 @map("completion_tokens")
  promptTokens           Int?                 @map("prompt_tokens")
  totalTokens            Int?                 @map("total_tokens")
  createdAt              DateTime             @default(now()) @map("created_at")
  updatedAt              DateTime             @updatedAt @map("updated_at")
  assistantMessageTypeId Int                  @map("assistant_message_type_id")
  assistantChatUser      AssistantChatUser    @relation(fields: [assistantChatUserId], references: [id], onDelete: Cascade)
  assistantMessageType   AssistantMessageType @relation(fields: [assistantMessageTypeId], references: [id])

  @@index([assistantChatUserId, createdAt])
  @@index([createdAt])
  @@index([assistantMessageTypeId])
  @@index([totalTokens])
  @@map("assistant_messages")
}

model feedback_types {
  id          Int        @id @default(autoincrement())
  name        String     @unique
  description String?
  created_at  DateTime   @default(now())
  updated_at  DateTime
  feedbacks   Feedback[]
}

enum ChatType {
  PRIVATE      @map("private")
  TASK         @map("task")
  DEPARTMENT   @map("department")
  ORGANIZATION @map("organization")

  @@map("chat_types")
}

enum MessageType {
  TEXT    @map("text")
  IMAGE   @map("image")
  FILE    @map("file")
  STICKER @map("sticker")
  LINK    @map("link")

  @@map("message_types")
}

enum MessageStatus {
  SENDING   @map("sending")
  DELIVERED @map("delivered")
  READ      @map("read")
  FAILED    @map("failed")

  @@map("message_status")
}

enum UserRoleType {
  OWNER  @map("owner")
  ADMIN  @map("admin")
  MEMBER @map("member")
  BOT    @map("bot")

  @@map("user_role_types")
}
