/*
  Warnings:

  - You are about to drop the column `department_id` on the `feedbacks` table. All the data in the column will be lost.
  - You are about to drop the column `organization_id` on the `feedbacks` table. All the data in the column will be lost.
  - You are about to drop the column `task_id` on the `feedbacks` table. All the data in the column will be lost.
  - You are about to drop the column `type_id` on the `feedbacks` table. All the data in the column will be lost.
  - You are about to drop the `feedback_types` table. If the table is not empty, all the data it contains will be lost.
  - Made the column `is_accept` on table `feedback_users` required. This step will fail if there are existing NULL values in that column.
  - Made the column `is_discard` on table `feedback_users` required. This step will fail if there are existing NULL values in that column.
  - Added the required column `type` to the `feedbacks` table without a default value. This is not possible if the table is not empty.
  - Made the column `situation` on table `feedbacks` required. This step will fail if there are existing NULL values in that column.
  - Made the column `behavior` on table `feedbacks` required. This step will fail if there are existing NULL values in that column.
  - Made the column `impact` on table `feedbacks` required. This step will fail if there are existing NULL values in that column.
  - Made the column `actionable` on table `feedbacks` required. This step will fail if there are existing NULL values in that column.
  - Made the column `appreciation` on table `feedbacks` required. This step will fail if there are existing NULL values in that column.
  - Added the required column `growth_token` to the `feedbacks` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "feedback_types" AS ENUM ('private', 'task', 'department', 'organization');

-- DropForeignKey
ALTER TABLE "feedbacks" DROP CONSTRAINT "feedbacks_department_id_fkey";

-- DropForeignKey
ALTER TABLE "feedbacks" DROP CONSTRAINT "feedbacks_organization_id_fkey";

-- DropForeignKey
ALTER TABLE "feedbacks" DROP CONSTRAINT "feedbacks_task_id_fkey";

-- DropForeignKey
ALTER TABLE "feedbacks" DROP CONSTRAINT "feedbacks_type_id_fkey";

-- DropIndex
DROP INDEX "feedback_users_is_accept_idx";

-- DropIndex
DROP INDEX "feedback_users_is_discard_idx";

-- DropIndex
DROP INDEX "feedback_users_is_share_idx";

-- DropIndex
DROP INDEX "feedbacks_department_id_idx";

-- DropIndex
DROP INDEX "feedbacks_organization_id_idx";

-- DropIndex
DROP INDEX "feedbacks_task_id_idx";

-- DropIndex
DROP INDEX "feedbacks_type_id_idx";

-- AlterTable
ALTER TABLE "feedback_users" ALTER COLUMN "is_accept" SET NOT NULL,
ALTER COLUMN "is_accept" SET DEFAULT false,
ALTER COLUMN "is_discard" SET NOT NULL,
ALTER COLUMN "is_discard" SET DEFAULT false;

-- AlterTable
ALTER TABLE "feedbacks" DROP COLUMN "department_id",
DROP COLUMN "organization_id",
DROP COLUMN "task_id",
DROP COLUMN "type_id",
ADD COLUMN     "type" "feedback_types" NOT NULL,
ALTER COLUMN "situation" SET NOT NULL,
ALTER COLUMN "behavior" SET NOT NULL,
ALTER COLUMN "impact" SET NOT NULL,
ALTER COLUMN "actionable" SET NOT NULL,
ALTER COLUMN "appreciation" SET NOT NULL,
DROP COLUMN "growth_token",
ADD COLUMN     "growth_token" INTEGER NOT NULL;

-- DropTable
DROP TABLE "feedback_types";

-- CreateTable
CREATE TABLE "feedback_types_master" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "feedback_types_master_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "feedback_types_master_name_key" ON "feedback_types_master"("name");

-- CreateIndex
CREATE INDEX "feedbacks_type_idx" ON "feedbacks"("type");
